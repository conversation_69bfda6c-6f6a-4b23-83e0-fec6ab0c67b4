'use client';

import React, { useState, useEffect } from 'react';
import { ArrowUp, Clock, Eye, BookOpen } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';

interface EnhancedReadingProgressProps {
  className?: string;
  showTimeEstimate?: boolean;
  showDetailedProgress?: boolean;
  showBackToTop?: boolean;
}

export function EnhancedReadingProgress({ 
  className, 
  showTimeEstimate = true,
  showDetailedProgress = false,
  showBackToTop = true
}: EnhancedReadingProgressProps) {
  const t = useTranslations('blog');
  const [progress, setProgress] = useState(0);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [readingTime, setReadingTime] = useState(0);
  const [timeLeft, setTimeLeft] = useState(0);
  const [isReading, setIsReading] = useState(false);
  const [wordsRead, setWordsRead] = useState(0);
  const [totalWords, setTotalWords] = useState(0);

  useEffect(() => {
    let readingTimer: NodeJS.Timeout;
    let lastScrollTime = Date.now();

    const updateProgress = () => {
      const article = document.querySelector('article');
      if (!article) {
        // 回退到文档级别的进度计算
        const scrollTop = window.scrollY;
        const docHeight = document.documentElement.scrollHeight - window.innerHeight;
        const scrollPercent = docHeight > 0 ? (scrollTop / docHeight) * 100 : 0;
        const clampedProgress = Math.min(100, Math.max(0, scrollPercent));
        
        setProgress(clampedProgress);
        setShowScrollTop(scrollTop > 300);
        return;
      }

      // 基于文章内容的精确进度计算
      const rect = article.getBoundingClientRect();
      const articleTop = rect.top + window.pageYOffset;
      const articleHeight = rect.height;
      const windowHeight = window.innerHeight;
      const scrollTop = window.pageYOffset;

      // 计算文章阅读进度
      const startProgress = articleTop - windowHeight * 0.2; // 文章进入视口20%时开始计算
      const endProgress = articleTop + articleHeight - windowHeight * 0.8; // 文章离开视口80%时结束

      let articleProgress = 0;
      if (scrollTop < startProgress) {
        articleProgress = 0;
      } else if (scrollTop > endProgress) {
        articleProgress = 100;
      } else {
        articleProgress = ((scrollTop - startProgress) / (endProgress - startProgress)) * 100;
      }

      setProgress(Math.min(Math.max(articleProgress, 0), 100));
      setShowScrollTop(scrollTop > 300);

      // 检测是否正在阅读
      const now = Date.now();
      if (now - lastScrollTime > 100) { // 100ms内没有滚动认为在阅读
        setIsReading(true);
        clearTimeout(readingTimer);
        readingTimer = setTimeout(() => setIsReading(false), 2000); // 2秒后认为停止阅读
      }
      lastScrollTime = now;

      // 计算阅读时间和字数
      if (showTimeEstimate) {
        const words = article.textContent?.split(/\s+/).length || 0;
        const wordsPerMinute = 200; // 平均阅读速度
        const totalTime = Math.ceil(words / wordsPerMinute);
        const remaining = Math.ceil(totalTime * (1 - articleProgress / 100));
        const currentWordsRead = Math.floor(words * (articleProgress / 100));
        
        setTotalWords(words);
        setWordsRead(currentWordsRead);
        setReadingTime(totalTime);
        setTimeLeft(Math.max(0, remaining));
      }
    };

    const handleScroll = () => {
      requestAnimationFrame(updateProgress);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    updateProgress(); // 初始计算

    return () => {
      window.removeEventListener('scroll', handleScroll);
      clearTimeout(readingTimer);
    };
  }, [showTimeEstimate]);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  const getProgressColor = () => {
    if (progress < 25) return 'bg-red-500';
    if (progress < 50) return 'bg-yellow-500';
    if (progress < 75) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getProgressColorClass = () => {
    if (progress < 25) return 'text-red-500';
    if (progress < 50) return 'text-yellow-500';
    if (progress < 75) return 'text-blue-500';
    return 'text-green-500';
  };

  return (
    <>
      {/* Top Progress Bar - Medium风格 */}
      <div
        className={cn(
          'fixed top-0 left-0 w-full h-1 bg-transparent z-50',
          className
        )}
      >
        <div
          className={cn(
            'h-full transition-all duration-300 ease-out shadow-sm',
            isReading ? 'bg-green-500' : getProgressColor()
          )}
          style={{ width: `${progress}%` }}
        />
      </div>

      {/* Detailed Progress Info - 左下角 */}
      {showDetailedProgress && showTimeEstimate && progress > 0 && (
        <div className={cn(
          'fixed bottom-4 left-4 z-40 bg-white dark:bg-dark-800',
          'border border-mystical-200 dark:border-dark-600 rounded-xl',
          'shadow-mystical-lg p-4 min-w-[280px]',
          'transition-all duration-300',
          'backdrop-blur-sm bg-white/95 dark:bg-dark-800/95'
        )}>
          {/* Header */}
          <div className="flex items-center gap-2 mb-3">
            <BookOpen className="w-4 h-4 text-mystical-500" />
            <span className="text-sm font-semibold text-mystical-900 dark:text-white">
              {t('readingProgress')}
            </span>
          </div>

          {/* Progress Bar */}
          <div className="mb-3">
            <div className="flex items-center justify-between text-xs text-mystical-500 dark:text-mystical-400 mb-2">
              <span>{Math.round(progress)}% {t('completed')}</span>
              <span className={cn('font-medium', getProgressColorClass())}>
                {progress < 25 ? t('justStarted') : 
                 progress < 50 ? t('gettingThere') :
                 progress < 75 ? t('almostDone') : t('nearlyFinished')}
              </span>
            </div>
            <div className="w-full bg-mystical-100 dark:bg-dark-600 rounded-full h-2">
              <div
                className={cn(
                  'h-2 rounded-full transition-all duration-300',
                  isReading ? 'bg-green-500' : getProgressColor()
                )}
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="flex items-center gap-2">
              <Clock className="w-3 h-3 text-mystical-400" />
              <div>
                <div className="text-mystical-600 dark:text-mystical-300 font-medium">
                  {timeLeft}min {t('left')}
                </div>
                <div className="text-mystical-400 dark:text-mystical-500">
                  {readingTime}min {t('total')}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Eye className="w-3 h-3 text-mystical-400" />
              <div>
                <div className="text-mystical-600 dark:text-mystical-300 font-medium">
                  {wordsRead.toLocaleString()} {t('words')}
                </div>
                <div className="text-mystical-400 dark:text-mystical-500">
                  {totalWords.toLocaleString()} {t('total')}
                </div>
              </div>
            </div>
          </div>

          {/* Reading Status */}
          <div className="mt-3 pt-3 border-t border-mystical-200 dark:border-dark-600">
            <div className="flex items-center gap-2">
              <div className={cn(
                'w-2 h-2 rounded-full transition-colors',
                isReading ? 'bg-green-500' : 'bg-mystical-300'
              )} />
              <span className="text-xs text-mystical-500 dark:text-mystical-400">
                {isReading ? t('currentlyReading') : t('paused')}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Circular Progress Indicator with Back to Top - 右下角 */}
      {showBackToTop && showScrollTop && (
        <button
          onClick={scrollToTop}
          className={cn(
            'fixed right-6 bottom-6 w-16 h-16 rounded-full z-50',
            'bg-white dark:bg-dark-800 border-2 border-mystical-200 dark:border-dark-600',
            'shadow-mystical-lg hover:shadow-mystical-xl',
            'transition-all duration-300 hover:-translate-y-1 hover:scale-105',
            'flex items-center justify-center group'
          )}
          title={t('backToTop')}
        >
          {/* Progress Ring */}
          <svg
            className="absolute inset-0 w-full h-full -rotate-90"
            viewBox="0 0 64 64"
          >
            <circle
              cx="32"
              cy="32"
              r="28"
              fill="none"
              stroke="currentColor"
              strokeWidth="3"
              className="text-mystical-100 dark:text-dark-600"
            />
            <circle
              cx="32"
              cy="32"
              r="28"
              fill="none"
              stroke="currentColor"
              strokeWidth="3"
              strokeLinecap="round"
              className={cn(
                'transition-all duration-300',
                isReading ? 'text-green-500' : getProgressColorClass()
              )}
              style={{
                strokeDasharray: `${2 * Math.PI * 28}`,
                strokeDashoffset: `${2 * Math.PI * 28 * (1 - progress / 100)}`,
              }}
            />
          </svg>
          
          {/* Arrow Icon */}
          <ArrowUp className="w-6 h-6 text-mystical-600 dark:text-mystical-400 group-hover:text-mystical-700 dark:group-hover:text-mystical-300 transition-colors" />
          
          {/* Progress Percentage Tooltip */}
          <div className={cn(
            'absolute -top-14 left-1/2 transform -translate-x-1/2',
            'bg-dark-900 text-white text-xs font-medium px-3 py-2 rounded-lg',
            'opacity-0 group-hover:opacity-100 transition-opacity duration-200',
            'pointer-events-none whitespace-nowrap',
            'after:content-[""] after:absolute after:top-full after:left-1/2',
            'after:transform after:-translate-x-1/2 after:border-4',
            'after:border-transparent after:border-t-dark-900'
          )}>
            <div className="text-center">
              <div className="font-bold">{Math.round(progress)}%</div>
              {showTimeEstimate && (
                <div className="text-xs opacity-75">
                  {timeLeft > 0 ? `${timeLeft}min ${t('left')}` : t('completed')}
                </div>
              )}
            </div>
          </div>
        </button>
      )}
    </>
  );
}
