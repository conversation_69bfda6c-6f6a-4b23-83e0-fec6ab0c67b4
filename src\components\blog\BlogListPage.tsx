'use client';

import React, { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { Search, Filter, Grid, List as ListIcon } from 'lucide-react';
import { BlogPost, BlogCategory, BlogTag } from '@/types';
import { cn } from '@/lib/utils';
import { BlogCard } from './BlogCard';
import { BlogPagination } from './BlogPagination';
import { BlogSidebar } from './BlogSidebar';

interface BlogListPageProps {
  posts: BlogPost[];
  featuredPosts?: BlogPost[];
  categories: BlogCategory[];
  tags: BlogTag[];
  currentCategory?: string;
  currentTag?: string;
  searchQuery?: string;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  className?: string;
}

export function BlogListPage({
  posts,
  featuredPosts = [],
  categories,
  tags,
  currentCategory,
  currentTag,
  searchQuery,
  pagination,
  className,
}: BlogListPageProps) {
  const t = useTranslations('blog');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [searchTerm, setSearchTerm] = useState(searchQuery || '');
  const [showFilters, setShowFilters] = useState(false);

  // 页面标题和描述
  const getPageTitle = () => {
    if (currentCategory) {
      const category = categories.find(cat => cat.slug === currentCategory);
      return category ? `${category.name} - ${t('articles')}` : t('blogTitle');
    }
    if (currentTag) {
      const tag = tags.find(t => t.slug === currentTag);
      return tag ? `#${tag.name} - ${t('articles')}` : t('blogTitle');
    }
    if (searchQuery) {
      return `${t('searchResults')}: "${searchQuery}"`;
    }
    return t('blogTitle');
  };

  const getPageDescription = () => {
    if (currentCategory) {
      const category = categories.find(cat => cat.slug === currentCategory);
      return category?.description || t('categoryDescription');
    }
    if (currentTag) {
      return t('tagDescription');
    }
    if (searchQuery) {
      return t('searchDescription');
    }
    return t('blogDescription');
  };

  return (
    <div className={cn('min-h-screen bg-white dark:bg-dark-900', className)}>
      <div className="mx-auto max-w-7xl px-4 py-8 md:px-6 lg:px-8">
        {/* Page Header */}
        <header className="mb-12 text-center">
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold font-serif text-mystical-900 dark:text-white mb-4">
            {getPageTitle()}
          </h1>
          <p className="text-lg text-mystical-600 dark:text-mystical-300 max-w-3xl mx-auto leading-[1.6]">
            {getPageDescription()}
          </p>

          {/* Category Navigation */}
          <div className="mt-8 flex flex-wrap justify-center gap-3">
            <button
              className={cn(
                'px-4 py-2 rounded-full text-sm font-medium transition-colors',
                !currentCategory && !currentTag
                  ? 'bg-mystical-500 text-white'
                  : 'bg-mystical-100 dark:bg-dark-700 text-mystical-700 dark:text-mystical-300 hover:bg-mystical-200 dark:hover:bg-dark-600'
              )}
            >
              {t('allArticles')}
            </button>
            {categories.map((category) => (
              <button
                key={category.id}
                className={cn(
                  'px-4 py-2 rounded-full text-sm font-medium transition-colors',
                  currentCategory === category.slug
                    ? 'bg-mystical-500 text-white'
                    : 'bg-mystical-100 dark:bg-dark-700 text-mystical-700 dark:text-mystical-300 hover:bg-mystical-200 dark:hover:bg-dark-600'
                )}
              >
                {category.name}
              </button>
            ))}
          </div>
        </header>

        {/* Search and Filters */}
        <div className="mb-8 flex flex-col md:flex-row gap-4 items-center justify-between">
          {/* Search Bar */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-mystical-400" />
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder={t('searchPlaceholder')}
              className={cn(
                'w-full pl-10 pr-4 py-3 rounded-lg border border-mystical-200 dark:border-dark-600',
                'bg-white dark:bg-dark-800 text-mystical-800 dark:text-mystical-200',
                'placeholder-mystical-400 dark:placeholder-mystical-500',
                'focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent',
                'transition-colors'
              )}
            />
          </div>

          {/* View Mode and Filters */}
          <div className="flex items-center gap-3">
            {/* View Mode Toggle */}
            <div className="flex items-center bg-mystical-100 dark:bg-dark-700 rounded-lg p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={cn(
                  'p-2 rounded-md transition-colors',
                  viewMode === 'grid'
                    ? 'bg-white dark:bg-dark-600 text-mystical-600 dark:text-mystical-400 shadow-sm'
                    : 'text-mystical-500 dark:text-mystical-500 hover:text-mystical-600 dark:hover:text-mystical-400'
                )}
                title={t('gridView')}
              >
                <Grid className="w-4 h-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={cn(
                  'p-2 rounded-md transition-colors',
                  viewMode === 'list'
                    ? 'bg-white dark:bg-dark-600 text-mystical-600 dark:text-mystical-400 shadow-sm'
                    : 'text-mystical-500 dark:text-mystical-500 hover:text-mystical-600 dark:hover:text-mystical-400'
                )}
                title={t('listView')}
              >
                <ListIcon className="w-4 h-4" />
              </button>
            </div>

            {/* Filters Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={cn(
                'flex items-center gap-2 px-4 py-2 rounded-lg border border-mystical-200 dark:border-dark-600',
                'bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400',
                'hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors'
              )}
            >
              <Filter className="w-4 h-4" />
              {t('filters')}
            </button>
          </div>
        </div>

        {/* Filters Panel */}
        {showFilters && (
          <div className="mb-8 p-6 bg-mystical-50 dark:bg-dark-800 rounded-xl border border-mystical-200 dark:border-dark-600">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Category Filter */}
              <div>
                <h3 className="text-sm font-semibold text-mystical-900 dark:text-white mb-3">
                  {t('categories')}
                </h3>
                <div className="space-y-2">
                  {categories.slice(0, 5).map((category) => (
                    <label key={category.id} className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        className="rounded border-mystical-300 text-mystical-500 focus:ring-mystical-500"
                      />
                      <span className="text-sm text-mystical-700 dark:text-mystical-300">
                        {category.name} ({category.postCount})
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Tag Filter */}
              <div>
                <h3 className="text-sm font-semibold text-mystical-900 dark:text-white mb-3">
                  {t('popularTags')}
                </h3>
                <div className="flex flex-wrap gap-2">
                  {tags.slice(0, 8).map((tag) => (
                    <button
                      key={tag.id}
                      className={cn(
                        'px-3 py-1 rounded-full text-xs font-medium transition-colors',
                        currentTag === tag.slug
                          ? 'bg-mystical-500 text-white'
                          : 'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600'
                      )}
                    >
                      #{tag.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* Sort Options */}
              <div>
                <h3 className="text-sm font-semibold text-mystical-900 dark:text-white mb-3">
                  {t('sortBy')}
                </h3>
                <select className={cn(
                  'w-full px-3 py-2 rounded-lg border border-mystical-200 dark:border-dark-600',
                  'bg-white dark:bg-dark-700 text-mystical-800 dark:text-mystical-200',
                  'focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent'
                )}>
                  <option value="publishedAt">{t('newest')}</option>
                  <option value="viewCount">{t('mostViewed')}</option>
                  <option value="title">{t('alphabetical')}</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Articles Section */}
          <div className="lg:col-span-3">
            {/* Featured Posts */}
            {featuredPosts.length > 0 && !currentCategory && !currentTag && !searchQuery && (
              <section className="mb-12">
                <h2 className="text-2xl font-bold font-serif text-mystical-900 dark:text-white mb-6">
                  {t('featuredArticles')}
                </h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {featuredPosts.slice(0, 2).map((post) => (
                    <BlogCard
                      key={post.id}
                      post={post}
                      variant="featured"
                      className="h-full"
                    />
                  ))}
                </div>
              </section>
            )}

            {/* Regular Posts */}
            <section>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold font-serif text-mystical-900 dark:text-white">
                  {featuredPosts.length > 0 && !currentCategory && !currentTag && !searchQuery
                    ? t('latestArticles')
                    : t('articles')
                  }
                </h2>
                <span className="text-sm text-mystical-500 dark:text-mystical-400">
                  {t('totalResults', { count: pagination.total })}
                </span>
              </div>

              {/* Posts Grid/List */}
              {posts.length > 0 ? (
                <div className={cn(
                  'grid gap-6',
                  viewMode === 'grid'
                    ? 'grid-cols-1 md:grid-cols-2 xl:grid-cols-3'
                    : 'grid-cols-1'
                )}>
                  {posts.map((post) => (
                    <BlogCard
                      key={post.id}
                      post={post}
                      variant={viewMode === 'list' ? 'compact' : 'default'}
                      className="h-full"
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-lg text-mystical-600 dark:text-mystical-300 mb-4">
                    {t('noArticlesFound')}
                  </p>
                  <button className="text-mystical-500 hover:text-mystical-600 dark:hover:text-mystical-400 transition-colors">
                    {t('clearFilters')}
                  </button>
                </div>
              )}

              {/* Pagination */}
              {posts.length > 0 && pagination.totalPages > 1 && (
                <div className="mt-12">
                  <BlogPagination
                    currentPage={pagination.page}
                    totalPages={pagination.totalPages}
                    hasNext={pagination.hasNext}
                    hasPrev={pagination.hasPrev}
                  />
                </div>
              )}
            </section>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <BlogSidebar
              categories={categories}
              tags={tags}
              currentCategory={currentCategory}
              currentTag={currentTag}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
