import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// AI内容生成请求验证
const AIGenerateSchema = z.object({
  topic: z.string().min(1).max(200),
  locale: z.string().min(2).max(10),
  category: z.string().min(1).max(50),
  keywords: z.array(z.string()).optional().default([]),
  targetLength: z.enum(['short', 'medium', 'long']).optional().default('medium'),
  tone: z.enum(['professional', 'casual', 'mystical', 'educational']).optional().default('mystical'),
  includeImages: z.boolean().optional().default(false),
  autoPublish: z.boolean().optional().default(false),
  seoOptimized: z.boolean().optional().default(true),
});

// AI批量生成请求验证
const AIBatchGenerateSchema = z.object({
  topics: z.array(z.string()).min(1).max(10),
  locale: z.string().min(2).max(10),
  category: z.string().min(1).max(50),
  commonKeywords: z.array(z.string()).optional().default([]),
  targetLength: z.enum(['short', 'medium', 'long']).optional().default('medium'),
  tone: z.enum(['professional', 'casual', 'mystical', 'educational']).optional().default('mystical'),
  autoPublish: z.boolean().optional().default(false),
  batchDelay: z.number().min(0).max(60000).optional().default(1000), // 毫秒
});

// POST /api/blog/ai-generate - AI生成单篇文章
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = AIGenerateSchema.parse(body);

    // 调用AI服务生成内容
    const aiContent = await generateAIContent(data);

    // 生成唯一slug
    const slug = await generateUniqueSlug(aiContent.title, data.locale);

    // 处理图片（如果需要）
    let coverImage = null;
    if (data.includeImages && aiContent.suggestedImages?.length > 0) {
      coverImage = aiContent.suggestedImages[0];
    }

    // 创建博客文章
    const post = await prisma.blogPost.create({
      data: {
        title: aiContent.title,
        slug,
        content: aiContent.content,
        excerpt: aiContent.excerpt,
        coverImage,
        locale: data.locale,
        category: data.category,
        tags: [...data.keywords, ...aiContent.suggestedTags],
        status: data.autoPublish ? 'PUBLISHED' : 'DRAFT',
        publishedAt: data.autoPublish ? new Date() : null,
        readingTime: calculateReadingTime(aiContent.content),
        seoTitle: aiContent.seoTitle,
        seoDescription: aiContent.seoDescription,
        keywords: [...data.keywords, ...aiContent.seoKeywords],
        metadata: {
          ai: {
            model: aiContent.model,
            generatedAt: new Date(),
            prompt: aiContent.prompt,
            confidence: aiContent.confidence,
            processingTime: aiContent.processingTime,
          },
          generation: {
            topic: data.topic,
            targetLength: data.targetLength,
            tone: data.tone,
            seoOptimized: data.seoOptimized,
          },
        },
      },
    });

    // 记录操作日志
    await prisma.blogPostLog.create({
      data: {
        blogPostId: post.id,
        action: 'created',
        changes: {
          source: 'ai-generated',
          topic: data.topic,
          autoPublish: data.autoPublish,
        },
      },
    });

    // 如果启用SEO优化，进行SEO分析
    if (data.seoOptimized) {
      await performSEOAnalysis(post.id);
    }

    return NextResponse.json({
      success: true,
      data: post,
      message: 'AI content generated successfully',
      aiMetadata: {
        model: aiContent.model,
        confidence: aiContent.confidence,
        processingTime: aiContent.processingTime,
      },
    });
  } catch (error) {
    console.error('Error generating AI content:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to generate AI content',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// AI内容生成接口
interface AIContentRequest {
  topic: string;
  locale: string;
  category: string;
  keywords: string[];
  targetLength: 'short' | 'medium' | 'long';
  tone: 'professional' | 'casual' | 'mystical' | 'educational';
  seoOptimized: boolean;
}

interface AIContentResponse {
  title: string;
  content: string;
  excerpt: string;
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];
  suggestedTags: string[];
  suggestedImages?: string[];
  model: string;
  prompt: string;
  confidence: number;
  processingTime: number;
}

// AI内容生成函数（模拟实现）
async function generateAIContent(request: AIContentRequest): Promise<AIContentResponse> {
  const startTime = Date.now();
  
  // 这里应该调用实际的AI服务（如通义千问、豆包等）
  // 目前提供模拟实现
  
  const lengthMap = {
    short: { min: 500, max: 1000 },
    medium: { min: 1000, max: 2000 },
    long: { min: 2000, max: 4000 },
  };

  const targetWords = lengthMap[request.targetLength];
  
  // 模拟AI生成的内容
  const mockContent = generateMockContent(request, targetWords);
  
  const processingTime = Date.now() - startTime;

  return {
    title: mockContent.title,
    content: mockContent.content,
    excerpt: mockContent.excerpt,
    seoTitle: mockContent.seoTitle,
    seoDescription: mockContent.seoDescription,
    seoKeywords: mockContent.seoKeywords,
    suggestedTags: mockContent.suggestedTags,
    suggestedImages: mockContent.suggestedImages,
    model: 'qwen-turbo', // 模拟使用通义千问
    prompt: `Generate a ${request.tone} ${request.targetLength} article about "${request.topic}" in ${request.locale} for ${request.category} category`,
    confidence: 0.85,
    processingTime,
  };
}

// 生成模拟内容
function generateMockContent(request: AIContentRequest, targetWords: { min: number; max: number }) {
  const { topic, category, keywords, tone, locale } = request;
  
  // 根据分类生成相关内容
  const categoryTemplates = {
    tarot: {
      title: `${topic} - 塔罗牌深度解读与指导`,
      contentTemplate: `探索${topic}的神秘世界，通过塔罗牌的智慧获得人生指导...`,
      tags: ['塔罗牌', '占卜', '神秘学', '人生指导'],
    },
    astrology: {
      title: `${topic} - 星座运势与性格分析`,
      contentTemplate: `深入了解${topic}在星座学中的意义，揭示性格特质与命运走向...`,
      tags: ['星座', '占星学', '性格分析', '运势'],
    },
    numerology: {
      title: `${topic} - 数字命理学解析`,
      contentTemplate: `通过数字命理学探索${topic}的深层含义，发现数字背后的秘密...`,
      tags: ['数字命理', '生命数字', '命运分析'],
    },
    // 可以添加更多分类模板
  };

  const template = categoryTemplates[category as keyof typeof categoryTemplates] || {
    title: `${topic} - 深度解析与指导`,
    contentTemplate: `深入探索${topic}的各个方面...`,
    tags: ['神秘学', '指导', '分析'],
  };

  return {
    title: template.title,
    content: generateDetailedContent(template.contentTemplate, targetWords.min),
    excerpt: `本文深入探讨${topic}，为您提供专业的分析和实用的指导建议。`,
    seoTitle: `${topic} - 专业解析 | 神秘学指导`,
    seoDescription: `专业解析${topic}，提供深入的分析和实用指导。了解更多神秘学知识，获得人生智慧。`,
    seoKeywords: [...keywords, ...template.tags, topic],
    suggestedTags: template.tags,
    suggestedImages: [`/images/blog/${category}/${topic.toLowerCase().replace(/\s+/g, '-')}.jpg`],
  };
}

// 生成详细内容
function generateDetailedContent(template: string, minWords: number): string {
  // 这里应该调用真实的AI服务生成内容
  // 目前返回模拟的HTML内容
  return `
    <h2>引言</h2>
    <p>${template}</p>
    
    <h2>深入分析</h2>
    <p>在这个部分，我们将深入探讨相关的各个方面，为您提供全面的理解和指导。</p>
    
    <h3>核心要点</h3>
    <ul>
      <li>要点一：详细解释和分析</li>
      <li>要点二：实用的建议和指导</li>
      <li>要点三：深层的洞察和理解</li>
    </ul>
    
    <h2>实践应用</h2>
    <p>了解理论知识后，让我们看看如何在实际生活中应用这些智慧。</p>
    
    <blockquote>
      <p>智慧不在于知道答案，而在于知道如何寻找答案。</p>
    </blockquote>
    
    <h2>总结</h2>
    <p>通过本文的探讨，我们希望您能够获得新的洞察和指导，在人生的道路上更加明智地前行。</p>
  `.trim();
}

// 生成唯一slug
async function generateUniqueSlug(title: string, locale: string): Promise<string> {
  let baseSlug = title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '')
    .replace(/[\s_-]+/g, '-')
    .replace(/^-+|-+$/g, '')
    .substring(0, 200);

  if (locale !== 'en') {
    baseSlug += `-${locale}`;
  }

  let slug = baseSlug;
  let counter = 1;

  while (await prisma.blogPost.findUnique({ where: { slug } })) {
    slug = `${baseSlug}-${counter}`;
    counter++;
  }

  return slug;
}

// 计算阅读时间
function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200;
  const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

// 执行SEO分析
async function performSEOAnalysis(postId: string): Promise<void> {
  // 这里应该实现实际的SEO分析逻辑
  // 目前创建一个模拟的SEO分析记录
  await prisma.blogSeoAnalysis.create({
    data: {
      blogPostId: postId,
      seoScore: 85,
      titleScore: 90,
      descriptionScore: 85,
      contentScore: 80,
      keywordDensity: 2.5,
      suggestions: {
        improvements: [
          '考虑在内容中增加更多相关关键词',
          '优化图片的alt标签',
          '添加内部链接提升SEO效果',
        ],
        strengths: [
          '标题长度适中',
          '描述信息完整',
          '内容结构清晰',
        ],
      },
    },
  });
}
