import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { BlogPostStatus } from '@/types';
import { z } from 'zod';

// 博客文章查询参数验证
const BlogQuerySchema = z.object({
  page: z.string().optional().default('1'),
  limit: z.string().optional().default('10'),
  locale: z.string().optional(),
  category: z.string().optional(),
  status: z.enum(['DRAFT', 'PENDING', 'SCHEDULED', 'PUBLISHED', 'ARCHIVED', 'DELETED']).optional(),
  featured: z.string().optional(),
  search: z.string().optional(),
  tags: z.string().optional(), // 逗号分隔的标签
  sortBy: z.enum(['createdAt', 'publishedAt', 'viewCount', 'title']).optional().default('publishedAt'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

// 博客文章创建验证
const CreateBlogPostSchema = z.object({
  title: z.string().min(1).max(200),
  slug: z.string().min(1).max(250).optional(),
  content: z.string().min(1),
  excerpt: z.string().max(500).optional(),
  coverImage: z.string().url().optional(),
  locale: z.string().min(2).max(10),
  category: z.string().min(1).max(50),
  tags: z.array(z.string()).optional().default([]),
  status: z.enum(['DRAFT', 'PENDING', 'SCHEDULED', 'PUBLISHED']).optional().default('DRAFT'),
  publishedAt: z.string().datetime().optional(),
  scheduledAt: z.string().datetime().optional(),
  featured: z.boolean().optional().default(false),
  seoTitle: z.string().max(60).optional(),
  seoDescription: z.string().max(160).optional(),
  keywords: z.array(z.string()).optional().default([]),
  metadata: z.record(z.any()).optional(),
});

// GET /api/blog - 获取博客文章列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = BlogQuerySchema.parse(Object.fromEntries(searchParams));

    const page = parseInt(query.page);
    const limit = parseInt(query.limit);
    const offset = (page - 1) * limit;

    // 构建查询条件
    const where: any = {};

    if (query.locale) {
      where.locale = query.locale;
    }

    if (query.category) {
      where.category = query.category;
    }

    if (query.status) {
      where.status = query.status;
    } else {
      // 默认只返回已发布的文章
      where.status = 'PUBLISHED';
    }

    if (query.featured) {
      where.featured = query.featured === 'true';
    }

    if (query.search) {
      where.OR = [
        { title: { contains: query.search, mode: 'insensitive' } },
        { content: { contains: query.search, mode: 'insensitive' } },
        { excerpt: { contains: query.search, mode: 'insensitive' } },
      ];
    }

    if (query.tags) {
      const tagList = query.tags.split(',').map(tag => tag.trim());
      where.tags = {
        hasSome: tagList,
      };
    }

    // 构建排序条件
    const orderBy: any = {};
    orderBy[query.sortBy] = query.sortOrder;

    // 执行查询
    const [posts, total] = await Promise.all([
      prisma.blogPost.findMany({
        where,
        orderBy,
        skip: offset,
        take: limit,
        select: {
          id: true,
          title: true,
          slug: true,
          excerpt: true,
          coverImage: true,
          locale: true,
          category: true,
          tags: true,
          status: true,
          publishedAt: true,
          viewCount: true,
          readingTime: true,
          featured: true,
          seoTitle: true,
          seoDescription: true,
          keywords: true,
          createdAt: true,
          updatedAt: true,
          _count: {
            select: {
              views: true,
              comments: true,
              favorites: true,
            },
          },
        },
      }),
      prisma.blogPost.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: posts,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });
  } catch (error) {
    console.error('Error fetching blog posts:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch blog posts',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// POST /api/blog - 创建博客文章
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const data = CreateBlogPostSchema.parse(body);

    // 生成slug（如果未提供）
    if (!data.slug) {
      data.slug = generateSlug(data.title, data.locale);
    }

    // 确保slug唯一性
    const existingPost = await prisma.blogPost.findUnique({
      where: { slug: data.slug },
    });

    if (existingPost) {
      data.slug = `${data.slug}-${Date.now()}`;
    }

    // 计算阅读时间
    const readingTime = calculateReadingTime(data.content);

    // 处理发布时间
    let publishedAt = null;
    if (data.status === 'PUBLISHED') {
      publishedAt = data.publishedAt ? new Date(data.publishedAt) : new Date();
    }

    let scheduledAt = null;
    if (data.status === 'SCHEDULED' && data.scheduledAt) {
      scheduledAt = new Date(data.scheduledAt);
    }

    // 创建博客文章
    const post = await prisma.blogPost.create({
      data: {
        title: data.title,
        slug: data.slug,
        content: data.content,
        excerpt: data.excerpt || generateExcerpt(data.content),
        coverImage: data.coverImage,
        locale: data.locale,
        category: data.category,
        tags: data.tags,
        status: data.status as any,
        publishedAt,
        scheduledAt,
        readingTime,
        featured: data.featured,
        seoTitle: data.seoTitle,
        seoDescription: data.seoDescription,
        keywords: data.keywords,
        metadata: data.metadata,
      },
    });

    return NextResponse.json({
      success: true,
      data: post,
      message: 'Blog post created successfully',
    });
  } catch (error) {
    console.error('Error creating blog post:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create blog post',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// 辅助函数：生成slug
function generateSlug(title: string, locale: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, '') // 移除特殊字符
    .replace(/[\s_-]+/g, '-') // 替换空格和下划线为连字符
    .replace(/^-+|-+$/g, '') // 移除开头和结尾的连字符
    .substring(0, 200) + (locale !== 'en' ? `-${locale}` : '');
}

// 辅助函数：计算阅读时间
function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200; // 平均阅读速度
  const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

// 辅助函数：生成摘要
function generateExcerpt(content: string, maxLength: number = 300): string {
  const plainText = content.replace(/<[^>]*>/g, '').trim();
  if (plainText.length <= maxLength) {
    return plainText;
  }
  return plainText.substring(0, maxLength).replace(/\s+\S*$/, '') + '...';
}
