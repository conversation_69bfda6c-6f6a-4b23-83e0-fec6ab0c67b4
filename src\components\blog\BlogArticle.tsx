'use client';

import React from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Calendar, Clock, Eye, Heart, Share2, User, BookOpen } from 'lucide-react';
import { BlogPost } from '@/types';
import { cn } from '@/lib/utils';
import { ReadingProgress } from './ReadingProgress';
import { EnhancedReadingProgress } from './EnhancedReadingProgress';
import { FocusMode } from './FocusMode';
import { BlogTableOfContents } from './BlogTableOfContents';
import { BlogInteractions } from './BlogInteractions';
import { RelatedPosts } from './RelatedPosts';
import { BlogComments } from './BlogComments';

interface BlogArticleProps {
  post: BlogPost;
  relatedPosts?: BlogPost[];
  className?: string;
}

export function BlogArticle({ post, relatedPosts, className }: BlogArticleProps) {
  const t = useTranslations('blog');

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat(post.locale, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    }).format(date);
  };

  return (
    <div className={cn('min-h-screen bg-white dark:bg-dark-900', className)}>
      {/* Enhanced Reading Progress */}
      <EnhancedReadingProgress
        showTimeEstimate={true}
        showDetailedProgress={true}
        showBackToTop={true}
      />

      {/* Focus Mode Toggle */}
      <FocusMode />

      {/* Article Container - Medium标准680px宽度 */}
      <article className="mx-auto max-w-[680px] px-4 py-8 md:px-6 lg:px-8">
        {/* Article Header */}
        <header className="mb-12">
          {/* Category Badge */}
          <div className="mb-6">
            <Link
              href={`/blog/${post.category.slug}`}
              className={cn(
                'inline-flex items-center px-4 py-2 rounded-full text-sm font-semibold',
                'bg-mystical-100 dark:bg-dark-700 text-mystical-700 dark:text-mystical-300',
                'hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors',
                'uppercase tracking-wide'
              )}
            >
              {post.category.name}
            </Link>
          </div>

          {/* Article Title - Medium风格 */}
          <h1 className={cn(
            'text-3xl md:text-4xl lg:text-5xl font-bold font-serif',
            'text-mystical-900 dark:text-white leading-[1.2]',
            'mb-6 letter-spacing-tight'
          )}>
            {post.title}
          </h1>

          {/* Article Subtitle/Excerpt */}
          {post.excerpt && (
            <p className={cn(
              'text-lg md:text-xl text-mystical-600 dark:text-mystical-300',
              'leading-[1.5] mb-8 font-normal italic'
            )}>
              {post.excerpt}
            </p>
          )}

          {/* Author Section - Medium风格 */}
          <div className={cn(
            'flex items-center gap-4 pb-8 mb-8',
            'border-b border-mystical-200 dark:border-dark-700'
          )}>
            {/* Author Avatar */}
            {post.author.avatar && (
              <Image
                src={post.author.avatar}
                alt={post.author.name}
                width={48}
                height={48}
                className="rounded-full border-2 border-mystical-200 dark:border-dark-600"
              />
            )}

            <div className="flex-1 min-w-0">
              {/* Author Name */}
              <p className="text-base font-semibold text-mystical-900 dark:text-white mb-1">
                {post.author.name}
              </p>

              {/* Article Metadata */}
              <div className="flex items-center gap-4 text-sm text-mystical-500 dark:text-mystical-400">
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(post.publishedAt || post.createdAt)}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="w-4 h-4" />
                  <span>{post.readingTime} {t('readTime')}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Eye className="w-4 h-4" />
                  <span>{post.viewCount}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Cover Image */}
          {post.coverImage && (
            <div className="mb-8 -mx-4 md:-mx-6 lg:-mx-8">
              <Image
                src={post.coverImage}
                alt={post.title}
                width={800}
                height={450}
                className="w-full aspect-[16/9] object-cover rounded-xl"
                sizes="(max-width: 768px) 100vw, 800px"
                priority
              />
            </div>
          )}
        </header>

        {/* Article Content - Medium标准排版 */}
        <div className={cn(
          'prose prose-lg max-w-none',
          // 基础排版 - 1.75倍行高
          'prose-p:text-mystical-800 dark:prose-p:text-mystical-100',
          'prose-p:text-lg md:prose-p:text-xl prose-p:leading-[1.75]',
          'prose-p:mb-6 prose-p:font-serif prose-p:tracking-tight',
          
          // 标题样式
          'prose-headings:font-serif prose-headings:font-bold',
          'prose-headings:text-mystical-900 dark:prose-headings:text-white',
          'prose-h1:text-3xl md:prose-h1:text-4xl prose-h1:leading-[1.2] prose-h1:mb-6 prose-h1:mt-12',
          'prose-h2:text-2xl md:prose-h2:text-3xl prose-h2:leading-[1.3] prose-h2:mb-4 prose-h2:mt-10',
          'prose-h3:text-xl md:prose-h3:text-2xl prose-h3:leading-[1.4] prose-h3:mb-3 prose-h3:mt-8',
          
          // 列表样式
          'prose-ul:mb-6 prose-ol:mb-6',
          'prose-li:text-lg md:prose-li:text-xl prose-li:leading-[1.75]',
          'prose-li:text-mystical-800 dark:prose-li:text-mystical-100',
          'prose-li:mb-2',
          
          // 引用块样式
          'prose-blockquote:text-xl md:prose-blockquote:text-2xl',
          'prose-blockquote:italic prose-blockquote:font-serif',
          'prose-blockquote:text-mystical-700 dark:prose-blockquote:text-mystical-200',
          'prose-blockquote:border-l-4 prose-blockquote:border-mystical-400',
          'prose-blockquote:pl-8 prose-blockquote:py-6 prose-blockquote:my-8',
          'prose-blockquote:bg-mystical-50 dark:prose-blockquote:bg-dark-800',
          'prose-blockquote:rounded-r-lg',
          
          // 代码样式
          'prose-code:text-sm prose-code:bg-mystical-100 dark:prose-code:bg-dark-700',
          'prose-code:text-mystical-800 dark:prose-code:text-mystical-200',
          'prose-code:px-2 prose-code:py-1 prose-code:rounded',
          'prose-code:font-mono prose-code:font-normal',
          
          // 链接样式
          'prose-a:text-mystical-600 dark:prose-a:text-mystical-400',
          'prose-a:underline prose-a:decoration-mystical-300',
          'prose-a:underline-offset-2 prose-a:decoration-1',
          'hover:prose-a:text-mystical-700 dark:hover:prose-a:text-mystical-300',
          'hover:prose-a:decoration-mystical-500',
          
          // 图片样式
          'prose-img:rounded-lg prose-img:shadow-mystical',
          'prose-img:my-8',
          
          // 分隔线样式
          'prose-hr:border-mystical-200 dark:prose-hr:border-dark-700',
          'prose-hr:my-12'
        )}>
          {/* 首段特殊样式 */}
          <div 
            className="first-paragraph text-xl md:text-2xl font-normal text-mystical-900 dark:text-white leading-[1.6] mb-8"
            dangerouslySetInnerHTML={{ __html: getFirstParagraph(post.content) }}
          />
          
          {/* 其余内容 */}
          <div dangerouslySetInnerHTML={{ __html: getRemainingContent(post.content) }} />
        </div>

        {/* Article Footer */}
        <footer className="mt-16 pt-8 border-t border-mystical-200 dark:border-dark-700">
          {/* Tags */}
          {post.tags.length > 0 && (
            <div className="mb-8">
              <div className="flex flex-wrap gap-2">
                {post.tags.map((tag) => (
                  <Link
                    key={tag.id}
                    href={`/blog/tag/${tag.slug}`}
                    className={cn(
                      'inline-flex items-center px-3 py-1 rounded-full text-sm',
                      'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400',
                      'hover:bg-mystical-200 dark:hover:bg-dark-600 transition-colors',
                      'border border-mystical-200 dark:border-dark-600'
                    )}
                  >
                    #{tag.name}
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Social Share */}
          <div className="mb-8">
            <h3 className="text-base font-semibold text-mystical-800 dark:text-mystical-200 mb-4">
              {t('shareArticle')}
            </h3>
            <div className="flex gap-3">
              <button className={cn(
                'w-10 h-10 rounded-full border border-mystical-200 dark:border-dark-600',
                'bg-white dark:bg-dark-800 text-mystical-600 dark:text-mystical-400',
                'hover:bg-mystical-50 dark:hover:bg-dark-700 hover:border-mystical-300',
                'transition-all duration-200 hover:-translate-y-1',
                'flex items-center justify-center'
              )}>
                <Share2 className="w-4 h-4" />
              </button>
              {/* 可以添加更多社交分享按钮 */}
            </div>
          </div>

          {/* Author Bio */}
          {post.author.bio && (
            <div className={cn(
              'bg-mystical-50 dark:bg-dark-800 p-8 rounded-xl',
              'border border-mystical-200 dark:border-dark-700 mb-8'
            )}>
              <div className="flex items-start gap-4">
                {post.author.avatar && (
                  <Image
                    src={post.author.avatar}
                    alt={post.author.name}
                    width={80}
                    height={80}
                    className="rounded-full"
                  />
                )}
                <div className="flex-1">
                  <h4 className="text-xl font-bold text-mystical-900 dark:text-white mb-2">
                    {post.author.name}
                  </h4>
                  <p className="text-base text-mystical-600 dark:text-mystical-300 leading-[1.6] mb-4">
                    {post.author.bio}
                  </p>
                  <Link
                    href={`/author/${post.author.id}`}
                    className={cn(
                      'inline-flex items-center text-sm font-medium',
                      'text-mystical-600 hover:text-mystical-700',
                      'border border-mystical-300 hover:border-mystical-400',
                      'px-4 py-2 rounded-full transition-colors'
                    )}
                  >
                    {t('followAuthor')}
                  </Link>
                </div>
              </div>
            </div>
          )}
        </footer>
      </article>

      {/* Floating Elements */}
      <BlogTableOfContents post={post} />
      <BlogInteractions post={post} />

      {/* Related Posts */}
      {relatedPosts && relatedPosts.length > 0 && (
        <RelatedPosts posts={relatedPosts} />
      )}

      {/* Comments */}
      <BlogComments postId={post.id} />
    </div>
  );
}

// 辅助函数：提取首段
function getFirstParagraph(content: string): string {
  const match = content.match(/<p[^>]*>(.*?)<\/p>/);
  return match ? match[0] : '';
}

// 辅助函数：获取除首段外的其余内容
function getRemainingContent(content: string): string {
  return content.replace(/<p[^>]*>.*?<\/p>/, '');
}
