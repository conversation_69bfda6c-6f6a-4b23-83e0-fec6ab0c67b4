import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// 博客文章更新验证
const UpdateBlogPostSchema = z.object({
  title: z.string().min(1).max(200).optional(),
  slug: z.string().min(1).max(250).optional(),
  content: z.string().min(1).optional(),
  excerpt: z.string().max(500).optional(),
  coverImage: z.string().url().optional(),
  category: z.string().min(1).max(50).optional(),
  tags: z.array(z.string()).optional(),
  status: z.enum(['DRAFT', 'PENDING', 'SCHEDULED', 'PUBLISHED', 'ARCHIVED']).optional(),
  publishedAt: z.string().datetime().optional(),
  scheduledAt: z.string().datetime().optional(),
  featured: z.boolean().optional(),
  seoTitle: z.string().max(60).optional(),
  seoDescription: z.string().max(160).optional(),
  keywords: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/blog/[id] - 获取单个博客文章
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const includeContent = searchParams.get('includeContent') === 'true';
    const incrementView = searchParams.get('incrementView') === 'true';

    // 查找文章（通过ID或slug）
    const whereCondition = id.length === 25 ? { id } : { slug: id };

    const post = await prisma.blogPost.findUnique({
      where: whereCondition,
      select: {
        id: true,
        title: true,
        slug: true,
        excerpt: true,
        content: includeContent,
        coverImage: true,
        locale: true,
        category: true,
        tags: true,
        status: true,
        publishedAt: true,
        scheduledAt: true,
        viewCount: true,
        readingTime: true,
        featured: true,
        seoTitle: true,
        seoDescription: true,
        keywords: true,
        metadata: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            views: true,
            comments: true,
            favorites: true,
          },
        },
      },
    });

    if (!post) {
      return NextResponse.json(
        {
          success: false,
          error: 'Blog post not found',
        },
        { status: 404 }
      );
    }

    // 增加浏览量（如果请求）
    if (incrementView && post.status === 'PUBLISHED') {
      await prisma.blogPost.update({
        where: { id: post.id },
        data: {
          viewCount: {
            increment: 1,
          },
        },
      });

      // 记录浏览记录
      const userAgent = request.headers.get('user-agent');
      const forwarded = request.headers.get('x-forwarded-for');
      const ipAddress = forwarded ? forwarded.split(',')[0] : request.ip;

      await prisma.blogView.create({
        data: {
          postId: post.id,
          ipAddress,
          userAgent,
        },
      });
    }

    return NextResponse.json({
      success: true,
      data: post,
    });
  } catch (error) {
    console.error('Error fetching blog post:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch blog post',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// PUT /api/blog/[id] - 更新博客文章
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const body = await request.json();
    const data = UpdateBlogPostSchema.parse(body);

    // 检查文章是否存在
    const existingPost = await prisma.blogPost.findUnique({
      where: { id },
    });

    if (!existingPost) {
      return NextResponse.json(
        {
          success: false,
          error: 'Blog post not found',
        },
        { status: 404 }
      );
    }

    // 处理slug更新
    if (data.slug && data.slug !== existingPost.slug) {
      const slugExists = await prisma.blogPost.findUnique({
        where: { slug: data.slug },
      });

      if (slugExists) {
        return NextResponse.json(
          {
            success: false,
            error: 'Slug already exists',
          },
          { status: 400 }
        );
      }
    }

    // 重新计算阅读时间（如果内容更新）
    let readingTime = existingPost.readingTime;
    if (data.content) {
      readingTime = calculateReadingTime(data.content);
    }

    // 处理发布状态变更
    let publishedAt = existingPost.publishedAt;
    if (data.status === 'PUBLISHED' && existingPost.status !== 'PUBLISHED') {
      publishedAt = data.publishedAt ? new Date(data.publishedAt) : new Date();
    } else if (data.status !== 'PUBLISHED') {
      publishedAt = null;
    }

    let scheduledAt = existingPost.scheduledAt;
    if (data.status === 'SCHEDULED' && data.scheduledAt) {
      scheduledAt = new Date(data.scheduledAt);
    } else if (data.status !== 'SCHEDULED') {
      scheduledAt = null;
    }

    // 更新文章
    const updatedPost = await prisma.blogPost.update({
      where: { id },
      data: {
        ...data,
        readingTime,
        publishedAt,
        scheduledAt,
        excerpt: data.excerpt || (data.content ? generateExcerpt(data.content) : undefined),
      },
    });

    // 记录操作日志
    await prisma.blogPostLog.create({
      data: {
        blogPostId: id,
        action: 'updated',
        changes: data,
      },
    });

    return NextResponse.json({
      success: true,
      data: updatedPost,
      message: 'Blog post updated successfully',
    });
  } catch (error) {
    console.error('Error updating blog post:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Validation error',
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update blog post',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// DELETE /api/blog/[id] - 删除博客文章（软删除）
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const hardDelete = searchParams.get('hard') === 'true';

    // 检查文章是否存在
    const existingPost = await prisma.blogPost.findUnique({
      where: { id },
    });

    if (!existingPost) {
      return NextResponse.json(
        {
          success: false,
          error: 'Blog post not found',
        },
        { status: 404 }
      );
    }

    if (hardDelete) {
      // 硬删除：完全删除记录
      await prisma.blogPost.delete({
        where: { id },
      });

      return NextResponse.json({
        success: true,
        message: 'Blog post permanently deleted',
      });
    } else {
      // 软删除：标记为已删除
      const deletedPost = await prisma.blogPost.update({
        where: { id },
        data: {
          status: 'DELETED',
        },
      });

      // 记录操作日志
      await prisma.blogPostLog.create({
        data: {
          blogPostId: id,
          action: 'deleted',
        },
      });

      return NextResponse.json({
        success: true,
        data: deletedPost,
        message: 'Blog post moved to trash',
      });
    }
  } catch (error) {
    console.error('Error deleting blog post:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete blog post',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// 辅助函数：计算阅读时间
function calculateReadingTime(content: string): number {
  const wordsPerMinute = 200;
  const wordCount = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  return Math.ceil(wordCount / wordsPerMinute);
}

// 辅助函数：生成摘要
function generateExcerpt(content: string, maxLength: number = 300): string {
  const plainText = content.replace(/<[^>]*>/g, '').trim();
  if (plainText.length <= maxLength) {
    return plainText;
  }
  return plainText.substring(0, maxLength).replace(/\s+\S*$/, '') + '...';
}
