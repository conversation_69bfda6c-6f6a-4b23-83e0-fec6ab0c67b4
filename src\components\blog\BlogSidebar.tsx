'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useTranslations } from 'next-intl';
import { Search, TrendingUp, Tag, Calendar, Mail, Rss } from 'lucide-react';
import { BlogCategory, BlogTag, BlogPost } from '@/types';
import { cn } from '@/lib/utils';

interface BlogSidebarProps {
  categories: BlogCategory[];
  tags: BlogTag[];
  popularPosts?: BlogPost[];
  currentCategory?: string;
  currentTag?: string;
  className?: string;
}

export function BlogSidebar({
  categories,
  tags,
  popularPosts = [],
  currentCategory,
  currentTag,
  className,
}: BlogSidebarProps) {
  const t = useTranslations('blog');
  const [searchTerm, setSearchTerm] = useState('');
  const [email, setEmail] = useState('');

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      // TODO: 实现搜索功能
      console.log('Search:', searchTerm);
    }
  };

  const handleNewsletterSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (email.trim()) {
      // TODO: 实现邮件订阅功能
      console.log('Subscribe:', email);
      setEmail('');
    }
  };

  return (
    <aside className={cn('space-y-8', className)}>
      {/* Search Widget */}
      <div className="bg-white dark:bg-dark-800 rounded-xl p-6 border border-mystical-200 dark:border-dark-700 shadow-mystical">
        <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-4 flex items-center gap-2">
          <Search className="w-5 h-5" />
          {t('search')}
        </h3>
        <form onSubmit={handleSearch}>
          <div className="relative">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder={t('searchPlaceholder')}
              className={cn(
                'w-full pl-4 pr-10 py-3 rounded-lg border border-mystical-200 dark:border-dark-600',
                'bg-mystical-50 dark:bg-dark-700 text-mystical-800 dark:text-mystical-200',
                'placeholder-mystical-400 dark:placeholder-mystical-500',
                'focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent',
                'transition-colors'
              )}
            />
            <button
              type="submit"
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-mystical-400 hover:text-mystical-600 dark:hover:text-mystical-300 transition-colors"
            >
              <Search className="w-4 h-4" />
            </button>
          </div>
        </form>
      </div>

      {/* Popular Posts */}
      {popularPosts.length > 0 && (
        <div className="bg-white dark:bg-dark-800 rounded-xl p-6 border border-mystical-200 dark:border-dark-700 shadow-mystical">
          <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-4 flex items-center gap-2">
            <TrendingUp className="w-5 h-5" />
            {t('popularPosts')}
          </h3>
          <div className="space-y-4">
            {popularPosts.slice(0, 5).map((post, index) => (
              <div key={post.id} className="flex gap-3">
                <div className="flex-shrink-0 w-8 h-8 bg-mystical-500 text-white rounded-full flex items-center justify-center text-sm font-bold">
                  {index + 1}
                </div>
                <div className="flex-1 min-w-0">
                  <Link
                    href={`/blog/${post.category.slug}/${post.slug}`}
                    className="block text-sm font-medium text-mystical-900 dark:text-white hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors line-clamp-2 leading-[1.4] mb-1"
                  >
                    {post.title}
                  </Link>
                  <div className="flex items-center gap-2 text-xs text-mystical-500 dark:text-mystical-400">
                    <Calendar className="w-3 h-3" />
                    <span>{new Date(post.publishedAt || post.createdAt).toLocaleDateString()}</span>
                    <span>•</span>
                    <span>{post.viewCount} {t('views')}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Categories */}
      <div className="bg-white dark:bg-dark-800 rounded-xl p-6 border border-mystical-200 dark:border-dark-700 shadow-mystical">
        <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-4">
          {t('categories')}
        </h3>
        <div className="space-y-2">
          {categories.map((category) => (
            <Link
              key={category.id}
              href={`/blog/${category.slug}`}
              className={cn(
                'flex items-center justify-between p-3 rounded-lg transition-colors',
                currentCategory === category.slug
                  ? 'bg-mystical-100 dark:bg-dark-700 text-mystical-700 dark:text-mystical-300'
                  : 'hover:bg-mystical-50 dark:hover:bg-dark-700 text-mystical-600 dark:text-mystical-400'
              )}
            >
              <span className="font-medium">{category.name}</span>
              <span className="text-sm bg-mystical-200 dark:bg-dark-600 text-mystical-600 dark:text-mystical-400 px-2 py-1 rounded-full">
                {category.postCount}
              </span>
            </Link>
          ))}
        </div>
      </div>

      {/* Tag Cloud */}
      <div className="bg-white dark:bg-dark-800 rounded-xl p-6 border border-mystical-200 dark:border-dark-700 shadow-mystical">
        <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-4 flex items-center gap-2">
          <Tag className="w-5 h-5" />
          {t('popularTags')}
        </h3>
        <div className="flex flex-wrap gap-2">
          {tags.slice(0, 15).map((tag) => (
            <Link
              key={tag.id}
              href={`/blog/tag/${tag.slug}`}
              className={cn(
                'inline-flex items-center px-3 py-1 rounded-full text-sm font-medium transition-colors',
                currentTag === tag.slug
                  ? 'bg-mystical-500 text-white'
                  : 'bg-mystical-100 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400 hover:bg-mystical-200 dark:hover:bg-dark-600'
              )}
            >
              #{tag.name}
            </Link>
          ))}
        </div>
      </div>

      {/* Newsletter Subscription */}
      <div className="bg-gradient-to-br from-mystical-50 to-gold-50 dark:from-dark-800 dark:to-dark-700 rounded-xl p-6 border border-mystical-200 dark:border-dark-700 shadow-mystical">
        <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-2 flex items-center gap-2">
          <Mail className="w-5 h-5" />
          {t('newsletter')}
        </h3>
        <p className="text-sm text-mystical-600 dark:text-mystical-300 mb-4 leading-[1.5]">
          {t('newsletterDescription')}
        </p>
        <form onSubmit={handleNewsletterSubmit} className="space-y-3">
          <input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder={t('emailPlaceholder')}
            className={cn(
              'w-full px-4 py-3 rounded-lg border border-mystical-200 dark:border-dark-600',
              'bg-white dark:bg-dark-800 text-mystical-800 dark:text-mystical-200',
              'placeholder-mystical-400 dark:placeholder-mystical-500',
              'focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent',
              'transition-colors'
            )}
            required
          />
          <button
            type="submit"
            className={cn(
              'w-full px-4 py-3 rounded-lg font-medium transition-colors',
              'bg-mystical-500 hover:bg-mystical-600 text-white',
              'hover:transform hover:-translate-y-0.5 hover:shadow-mystical'
            )}
          >
            {t('subscribe')}
          </button>
        </form>
      </div>

      {/* RSS Feed */}
      <div className="bg-white dark:bg-dark-800 rounded-xl p-6 border border-mystical-200 dark:border-dark-700 shadow-mystical">
        <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-4 flex items-center gap-2">
          <Rss className="w-5 h-5" />
          {t('rssFeed')}
        </h3>
        <p className="text-sm text-mystical-600 dark:text-mystical-300 mb-4 leading-[1.5]">
          {t('rssDescription')}
        </p>
        <Link
          href="/rss.xml"
          className={cn(
            'inline-flex items-center gap-2 px-4 py-2 rounded-lg',
            'bg-orange-500 hover:bg-orange-600 text-white',
            'font-medium transition-colors',
            'hover:transform hover:-translate-y-0.5 hover:shadow-lg'
          )}
        >
          <Rss className="w-4 h-4" />
          {t('subscribeRss')}
        </Link>
      </div>

      {/* Archive */}
      <div className="bg-white dark:bg-dark-800 rounded-xl p-6 border border-mystical-200 dark:border-dark-700 shadow-mystical">
        <h3 className="text-lg font-bold text-mystical-900 dark:text-white mb-4">
          {t('archive')}
        </h3>
        <div className="space-y-2">
          {/* 这里可以添加按月份或年份的归档链接 */}
          <Link
            href="/blog/2024"
            className="block p-2 rounded-lg text-mystical-600 dark:text-mystical-400 hover:bg-mystical-50 dark:hover:bg-dark-700 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
          >
            2024年 (12)
          </Link>
          <Link
            href="/blog/2023"
            className="block p-2 rounded-lg text-mystical-600 dark:text-mystical-400 hover:bg-mystical-50 dark:hover:bg-dark-700 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
          >
            2023年 (24)
          </Link>
        </div>
      </div>
    </aside>
  );
}
