'use client';

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { MessageCircle, Heart, Reply, MoreHorizontal, Send } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Comment {
  id: string;
  content: string;
  author: {
    id: string;
    name: string;
    avatar?: string;
  };
  createdAt: Date;
  likes: number;
  isLiked: boolean;
  replies?: Comment[];
  parentId?: string;
}

interface BlogCommentsProps {
  postId: string;
  className?: string;
}

export function BlogComments({ postId, className }: BlogCommentsProps) {
  const t = useTranslations('blog');
  const [comments, setComments] = useState<Comment[]>([]);
  const [newComment, setNewComment] = useState('');
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [loading, setLoading] = useState(false);

  // 模拟评论数据
  useEffect(() => {
    // 这里应该从API获取评论数据
    const mockComments: Comment[] = [
      {
        id: '1',
        content: '这篇文章写得非常好，对我很有启发！特别是关于塔罗牌解读的部分，让我对神秘学有了更深的理解。',
        author: {
          id: 'user1',
          name: '神秘探索者',
          avatar: '/images/avatars/user1.jpg',
        },
        createdAt: new Date('2024-01-15T10:30:00'),
        likes: 12,
        isLiked: false,
        replies: [
          {
            id: '2',
            content: '我也有同样的感受，作者的见解很独到。',
            author: {
              id: 'user2',
              name: '星座爱好者',
              avatar: '/images/avatars/user2.jpg',
            },
            createdAt: new Date('2024-01-15T11:15:00'),
            likes: 3,
            isLiked: true,
            parentId: '1',
          },
        ],
      },
      {
        id: '3',
        content: '能否推荐一些相关的书籍？我想深入学习这方面的知识。',
        author: {
          id: 'user3',
          name: '学习者',
          avatar: '/images/avatars/user3.jpg',
        },
        createdAt: new Date('2024-01-16T09:20:00'),
        likes: 5,
        isLiked: false,
      },
    ];
    setComments(mockComments);
  }, [postId]);

  const formatDate = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days === 0) {
      const hours = Math.floor(diff / (1000 * 60 * 60));
      if (hours === 0) {
        const minutes = Math.floor(diff / (1000 * 60));
        return `${minutes}分钟前`;
      }
      return `${hours}小时前`;
    } else if (days < 7) {
      return `${days}天前`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    setLoading(true);
    try {
      // TODO: 实现评论提交API
      const comment: Comment = {
        id: Date.now().toString(),
        content: newComment,
        author: {
          id: 'current-user',
          name: '当前用户',
          avatar: '/images/avatars/default.jpg',
        },
        createdAt: new Date(),
        likes: 0,
        isLiked: false,
      };
      
      setComments([comment, ...comments]);
      setNewComment('');
    } catch (error) {
      console.error('Failed to submit comment:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmitReply = async (parentId: string) => {
    if (!replyContent.trim()) return;

    setLoading(true);
    try {
      // TODO: 实现回复提交API
      const reply: Comment = {
        id: Date.now().toString(),
        content: replyContent,
        author: {
          id: 'current-user',
          name: '当前用户',
          avatar: '/images/avatars/default.jpg',
        },
        createdAt: new Date(),
        likes: 0,
        isLiked: false,
        parentId,
      };

      setComments(comments.map(comment => {
        if (comment.id === parentId) {
          return {
            ...comment,
            replies: [...(comment.replies || []), reply],
          };
        }
        return comment;
      }));
      
      setReplyContent('');
      setReplyingTo(null);
    } catch (error) {
      console.error('Failed to submit reply:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleLikeComment = async (commentId: string) => {
    // TODO: 实现点赞API
    setComments(comments.map(comment => {
      if (comment.id === commentId) {
        return {
          ...comment,
          isLiked: !comment.isLiked,
          likes: comment.isLiked ? comment.likes - 1 : comment.likes + 1,
        };
      }
      if (comment.replies) {
        return {
          ...comment,
          replies: comment.replies.map(reply => {
            if (reply.id === commentId) {
              return {
                ...reply,
                isLiked: !reply.isLiked,
                likes: reply.isLiked ? reply.likes - 1 : reply.likes + 1,
              };
            }
            return reply;
          }),
        };
      }
      return comment;
    }));
  };

  return (
    <section id="comments" className={cn('bg-white dark:bg-dark-900 py-16', className)}>
      <div className="mx-auto max-w-4xl px-4 md:px-6 lg:px-8">
        {/* Comments Header */}
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl font-bold text-mystical-900 dark:text-white flex items-center gap-2">
            <MessageCircle className="w-6 h-6" />
            {t('comments')} ({comments.length})
          </h2>
        </div>

        {/* Comment Form */}
        <div className="mb-12">
          <form onSubmit={handleSubmitComment} className="space-y-4">
            <div>
              <textarea
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                placeholder={t('writeComment')}
                rows={4}
                className={cn(
                  'w-full px-4 py-3 rounded-lg border border-mystical-200 dark:border-dark-600',
                  'bg-white dark:bg-dark-800 text-mystical-800 dark:text-mystical-200',
                  'placeholder-mystical-400 dark:placeholder-mystical-500',
                  'focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent',
                  'resize-vertical transition-colors'
                )}
              />
            </div>
            <div className="flex items-center justify-between">
              <p className="text-sm text-mystical-500 dark:text-mystical-400">
                {t('commentGuidelines')}
              </p>
              <button
                type="submit"
                disabled={!newComment.trim() || loading}
                className={cn(
                  'inline-flex items-center gap-2 px-6 py-2 rounded-lg',
                  'bg-mystical-500 hover:bg-mystical-600 disabled:bg-mystical-300',
                  'text-white font-medium transition-colors',
                  'disabled:cursor-not-allowed'
                )}
              >
                <Send className="w-4 h-4" />
                {loading ? t('submitting') : t('submitComment')}
              </button>
            </div>
          </form>
        </div>

        {/* Comments List */}
        <div className="space-y-8">
          {comments.map((comment) => (
            <div key={comment.id} className="space-y-4">
              {/* Main Comment */}
              <div className={cn(
                'bg-mystical-50 dark:bg-dark-800 rounded-xl p-6',
                'border border-mystical-200 dark:border-dark-700'
              )}>
                {/* Comment Header */}
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {comment.author.avatar && (
                      <Image
                        src={comment.author.avatar}
                        alt={comment.author.name}
                        width={40}
                        height={40}
                        className="rounded-full"
                      />
                    )}
                    <div>
                      <h4 className="font-semibold text-mystical-900 dark:text-white">
                        {comment.author.name}
                      </h4>
                      <p className="text-sm text-mystical-500 dark:text-mystical-400">
                        {formatDate(comment.createdAt)}
                      </p>
                    </div>
                  </div>
                  <button className="text-mystical-400 hover:text-mystical-600 dark:hover:text-mystical-300">
                    <MoreHorizontal className="w-5 h-5" />
                  </button>
                </div>

                {/* Comment Content */}
                <p className="text-mystical-700 dark:text-mystical-300 leading-[1.6] mb-4">
                  {comment.content}
                </p>

                {/* Comment Actions */}
                <div className="flex items-center gap-4">
                  <button
                    onClick={() => handleLikeComment(comment.id)}
                    className={cn(
                      'flex items-center gap-1 text-sm transition-colors',
                      comment.isLiked
                        ? 'text-red-500'
                        : 'text-mystical-500 hover:text-red-500'
                    )}
                  >
                    <Heart className={cn('w-4 h-4', comment.isLiked && 'fill-current')} />
                    {comment.likes}
                  </button>
                  <button
                    onClick={() => setReplyingTo(replyingTo === comment.id ? null : comment.id)}
                    className="flex items-center gap-1 text-sm text-mystical-500 hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
                  >
                    <Reply className="w-4 h-4" />
                    {t('reply')}
                  </button>
                </div>

                {/* Reply Form */}
                {replyingTo === comment.id && (
                  <div className="mt-4 pt-4 border-t border-mystical-200 dark:border-dark-600">
                    <div className="flex gap-3">
                      <textarea
                        value={replyContent}
                        onChange={(e) => setReplyContent(e.target.value)}
                        placeholder={t('writeReply')}
                        rows={3}
                        className={cn(
                          'flex-1 px-3 py-2 rounded-lg border border-mystical-200 dark:border-dark-600',
                          'bg-white dark:bg-dark-700 text-mystical-800 dark:text-mystical-200',
                          'placeholder-mystical-400 dark:placeholder-mystical-500',
                          'focus:outline-none focus:ring-2 focus:ring-mystical-500 focus:border-transparent',
                          'resize-none transition-colors'
                        )}
                      />
                      <div className="flex flex-col gap-2">
                        <button
                          onClick={() => handleSubmitReply(comment.id)}
                          disabled={!replyContent.trim() || loading}
                          className={cn(
                            'px-4 py-2 rounded-lg bg-mystical-500 hover:bg-mystical-600',
                            'disabled:bg-mystical-300 text-white text-sm font-medium',
                            'transition-colors disabled:cursor-not-allowed'
                          )}
                        >
                          {t('reply')}
                        </button>
                        <button
                          onClick={() => {
                            setReplyingTo(null);
                            setReplyContent('');
                          }}
                          className="px-4 py-2 rounded-lg border border-mystical-200 dark:border-dark-600 text-mystical-600 dark:text-mystical-400 text-sm hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
                        >
                          {t('cancel')}
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Replies */}
              {comment.replies && comment.replies.length > 0 && (
                <div className="ml-8 space-y-4">
                  {comment.replies.map((reply) => (
                    <div
                      key={reply.id}
                      className={cn(
                        'bg-white dark:bg-dark-800 rounded-lg p-4',
                        'border border-mystical-200 dark:border-dark-700'
                      )}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          {reply.author.avatar && (
                            <Image
                              src={reply.author.avatar}
                              alt={reply.author.name}
                              width={32}
                              height={32}
                              className="rounded-full"
                            />
                          )}
                          <div>
                            <h5 className="font-medium text-mystical-900 dark:text-white text-sm">
                              {reply.author.name}
                            </h5>
                            <p className="text-xs text-mystical-500 dark:text-mystical-400">
                              {formatDate(reply.createdAt)}
                            </p>
                          </div>
                        </div>
                      </div>
                      <p className="text-mystical-700 dark:text-mystical-300 text-sm leading-[1.6] mb-3">
                        {reply.content}
                      </p>
                      <button
                        onClick={() => handleLikeComment(reply.id)}
                        className={cn(
                          'flex items-center gap-1 text-xs transition-colors',
                          reply.isLiked
                            ? 'text-red-500'
                            : 'text-mystical-500 hover:text-red-500'
                        )}
                      >
                        <Heart className={cn('w-3 h-3', reply.isLiked && 'fill-current')} />
                        {reply.likes}
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Load More Comments */}
        {comments.length > 0 && (
          <div className="text-center mt-8">
            <button className={cn(
              'px-6 py-3 rounded-lg border border-mystical-200 dark:border-dark-600',
              'text-mystical-600 dark:text-mystical-400',
              'hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors'
            )}>
              {t('loadMoreComments')}
            </button>
          </div>
        )}
      </div>
    </section>
  );
}
