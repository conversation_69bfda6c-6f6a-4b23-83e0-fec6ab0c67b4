'use client';

import React, { useState, useEffect } from 'react';
import { Heart, Share2, Bookmark, MessageCircle, Eye, Moon, Sun } from 'lucide-react';
import { useTheme } from 'next-themes';
import { useTranslations } from 'next-intl';
import { BlogPost } from '@/types';
import { cn } from '@/lib/utils';

interface BlogInteractionsProps {
  post: BlogPost;
  className?: string;
}

export function BlogInteractions({ post, className }: BlogInteractionsProps) {
  const { theme, setTheme } = useTheme();
  const t = useTranslations('blog');
  const [isLiked, setIsLiked] = useState(false);
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showShareMenu, setShowShareMenu] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleLike = async () => {
    setIsLiked(!isLiked);
    // TODO: 实现点赞API调用
  };

  const handleBookmark = async () => {
    setIsBookmarked(!isBookmarked);
    // TODO: 实现收藏API调用
  };

  const handleShare = (platform: string) => {
    const url = window.location.href;
    const title = post.title;
    
    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
      copy: url,
    };

    if (platform === 'copy') {
      navigator.clipboard.writeText(url);
      // TODO: 显示复制成功提示
    } else {
      window.open(shareUrls[platform as keyof typeof shareUrls], '_blank', 'width=600,height=400');
    }
    
    setShowShareMenu(false);
  };

  if (!mounted) return null;

  return (
    <>
      {/* Desktop Floating Interactions - 左侧固定 */}
      <div className={cn(
        'fixed left-8 bottom-8 hidden lg:flex flex-col gap-3 z-50',
        className
      )}>
        {/* Like Button */}
        <button
          onClick={handleLike}
          className={cn(
            'w-12 h-12 rounded-full border transition-all duration-300',
            'flex items-center justify-center group',
            'hover:transform hover:-translate-y-1 hover:shadow-mystical-lg',
            isLiked
              ? 'bg-red-500 border-red-500 text-white'
              : 'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-600 text-mystical-600 dark:text-mystical-400'
          )}
          title={t('likePost')}
        >
          <Heart className={cn('w-5 h-5', isLiked && 'fill-current')} />
        </button>

        {/* Bookmark Button */}
        <button
          onClick={handleBookmark}
          className={cn(
            'w-12 h-12 rounded-full border transition-all duration-300',
            'flex items-center justify-center group',
            'hover:transform hover:-translate-y-1 hover:shadow-mystical-lg',
            isBookmarked
              ? 'bg-mystical-500 border-mystical-500 text-white'
              : 'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-600 text-mystical-600 dark:text-mystical-400'
          )}
          title={t('bookmarkPost')}
        >
          <Bookmark className={cn('w-5 h-5', isBookmarked && 'fill-current')} />
        </button>

        {/* Share Button */}
        <div className="relative">
          <button
            onClick={() => setShowShareMenu(!showShareMenu)}
            className={cn(
              'w-12 h-12 rounded-full border transition-all duration-300',
              'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-600',
              'text-mystical-600 dark:text-mystical-400',
              'hover:transform hover:-translate-y-1 hover:shadow-mystical-lg',
              'flex items-center justify-center'
            )}
            title={t('sharePost')}
          >
            <Share2 className="w-5 h-5" />
          </button>

          {/* Share Dropdown */}
          {showShareMenu && (
            <div className={cn(
              'absolute bottom-full left-0 mb-2 w-40',
              'bg-white dark:bg-dark-800 border border-mystical-200 dark:border-dark-600',
              'rounded-lg shadow-mystical-lg p-2'
            )}>
              <button
                onClick={() => handleShare('twitter')}
                className="w-full text-left px-3 py-2 text-sm text-mystical-700 dark:text-mystical-300 hover:bg-mystical-50 dark:hover:bg-dark-700 rounded-md transition-colors"
              >
                Twitter
              </button>
              <button
                onClick={() => handleShare('facebook')}
                className="w-full text-left px-3 py-2 text-sm text-mystical-700 dark:text-mystical-300 hover:bg-mystical-50 dark:hover:bg-dark-700 rounded-md transition-colors"
              >
                Facebook
              </button>
              <button
                onClick={() => handleShare('linkedin')}
                className="w-full text-left px-3 py-2 text-sm text-mystical-700 dark:text-mystical-300 hover:bg-mystical-50 dark:hover:bg-dark-700 rounded-md transition-colors"
              >
                LinkedIn
              </button>
              <button
                onClick={() => handleShare('copy')}
                className="w-full text-left px-3 py-2 text-sm text-mystical-700 dark:text-mystical-300 hover:bg-mystical-50 dark:hover:bg-dark-700 rounded-md transition-colors"
              >
                {t('copyLink')}
              </button>
            </div>
          )}
        </div>

        {/* Comments Link */}
        <button
          onClick={() => {
            const commentsSection = document.getElementById('comments');
            commentsSection?.scrollIntoView({ behavior: 'smooth' });
          }}
          className={cn(
            'w-12 h-12 rounded-full border transition-all duration-300',
            'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-600',
            'text-mystical-600 dark:text-mystical-400',
            'hover:transform hover:-translate-y-1 hover:shadow-mystical-lg',
            'flex items-center justify-center'
          )}
          title={t('viewComments')}
        >
          <MessageCircle className="w-5 h-5" />
        </button>
      </div>

      {/* Desktop Theme Toggle - 右上角固定 */}
      <button
        onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
        className={cn(
          'fixed top-4 right-4 w-10 h-10 rounded-full border z-50',
          'bg-white dark:bg-dark-800 border-mystical-200 dark:border-dark-600',
          'text-mystical-600 dark:text-mystical-400',
          'hover:bg-mystical-50 dark:hover:bg-dark-700 hover:border-mystical-300',
          'transition-all duration-200 flex items-center justify-center',
          'hidden lg:flex'
        )}
        title={theme === 'dark' ? t('lightMode') : t('darkMode')}
      >
        {theme === 'dark' ? (
          <Sun className="w-5 h-5" />
        ) : (
          <Moon className="w-5 h-5" />
        )}
      </button>

      {/* Mobile Interactions Bar - 底部固定 */}
      <div className={cn(
        'fixed bottom-0 left-0 right-0 lg:hidden z-50',
        'bg-white dark:bg-dark-800 border-t border-mystical-200 dark:border-dark-700',
        'px-4 py-3 shadow-mystical-lg'
      )}>
        <div className="flex items-center justify-between max-w-md mx-auto">
          {/* Like */}
          <button
            onClick={handleLike}
            className={cn(
              'flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-colors',
              isLiked
                ? 'text-red-500'
                : 'text-mystical-600 dark:text-mystical-400 hover:bg-mystical-50 dark:hover:bg-dark-700'
            )}
          >
            <Heart className={cn('w-5 h-5', isLiked && 'fill-current')} />
            <span className="text-xs">{t('like')}</span>
          </button>

          {/* Bookmark */}
          <button
            onClick={handleBookmark}
            className={cn(
              'flex flex-col items-center gap-1 px-3 py-2 rounded-lg transition-colors',
              isBookmarked
                ? 'text-mystical-500'
                : 'text-mystical-600 dark:text-mystical-400 hover:bg-mystical-50 dark:hover:bg-dark-700'
            )}
          >
            <Bookmark className={cn('w-5 h-5', isBookmarked && 'fill-current')} />
            <span className="text-xs">{t('save')}</span>
          </button>

          {/* Share */}
          <button
            onClick={() => setShowShareMenu(!showShareMenu)}
            className="flex flex-col items-center gap-1 px-3 py-2 rounded-lg text-mystical-600 dark:text-mystical-400 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
          >
            <Share2 className="w-5 h-5" />
            <span className="text-xs">{t('share')}</span>
          </button>

          {/* Comments */}
          <button
            onClick={() => {
              const commentsSection = document.getElementById('comments');
              commentsSection?.scrollIntoView({ behavior: 'smooth' });
            }}
            className="flex flex-col items-center gap-1 px-3 py-2 rounded-lg text-mystical-600 dark:text-mystical-400 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
          >
            <MessageCircle className="w-5 h-5" />
            <span className="text-xs">{t('comment')}</span>
          </button>

          {/* Theme Toggle */}
          <button
            onClick={() => setTheme(theme === 'dark' ? 'light' : 'dark')}
            className="flex flex-col items-center gap-1 px-3 py-2 rounded-lg text-mystical-600 dark:text-mystical-400 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
          >
            {theme === 'dark' ? (
              <Sun className="w-5 h-5" />
            ) : (
              <Moon className="w-5 h-5" />
            )}
            <span className="text-xs">{theme === 'dark' ? t('light') : t('dark')}</span>
          </button>
        </div>
      </div>

      {/* Mobile Share Menu Overlay */}
      {showShareMenu && (
        <div className="fixed inset-0 bg-black/50 z-50 lg:hidden" onClick={() => setShowShareMenu(false)}>
          <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-dark-800 rounded-t-xl p-6">
            <h3 className="text-lg font-semibold text-mystical-900 dark:text-white mb-4">
              {t('sharePost')}
            </h3>
            <div className="grid grid-cols-2 gap-3">
              <button
                onClick={() => handleShare('twitter')}
                className="flex items-center gap-3 p-3 rounded-lg border border-mystical-200 dark:border-dark-600 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
              >
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  T
                </div>
                <span className="text-mystical-700 dark:text-mystical-300">Twitter</span>
              </button>
              <button
                onClick={() => handleShare('facebook')}
                className="flex items-center gap-3 p-3 rounded-lg border border-mystical-200 dark:border-dark-600 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
              >
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  f
                </div>
                <span className="text-mystical-700 dark:text-mystical-300">Facebook</span>
              </button>
              <button
                onClick={() => handleShare('linkedin')}
                className="flex items-center gap-3 p-3 rounded-lg border border-mystical-200 dark:border-dark-600 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
              >
                <div className="w-8 h-8 bg-blue-700 rounded-full flex items-center justify-center text-white text-sm font-bold">
                  in
                </div>
                <span className="text-mystical-700 dark:text-mystical-300">LinkedIn</span>
              </button>
              <button
                onClick={() => handleShare('copy')}
                className="flex items-center gap-3 p-3 rounded-lg border border-mystical-200 dark:border-dark-600 hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
              >
                <div className="w-8 h-8 bg-mystical-500 rounded-full flex items-center justify-center text-white">
                  <Share2 className="w-4 h-4" />
                </div>
                <span className="text-mystical-700 dark:text-mystical-300">{t('copyLink')}</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
