'use client';

import React, { useState, useEffect } from 'react';
import { Eye, EyeOff, Maximize, Minimize } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';

interface FocusModeProps {
  className?: string;
}

export function FocusMode({ className }: FocusModeProps) {
  const t = useTranslations('blog');
  const [isFocusMode, setIsFocusMode] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      setIsVisible(scrollTop > 200); // 滚动200px后显示
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // 初始检查

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  useEffect(() => {
    if (isFocusMode) {
      // 进入专注模式
      document.body.classList.add('focus-mode');
      
      // 隐藏不必要的元素
      const elementsToHide = [
        'header',
        'nav',
        'aside',
        '.blog-sidebar',
        '.blog-interactions',
        '.related-posts',
        '.blog-comments',
        'footer',
      ];

      elementsToHide.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          (el as HTMLElement).style.display = 'none';
        });
      });

      // 调整文章容器样式
      const articleContainer = document.querySelector('article');
      if (articleContainer) {
        (articleContainer as HTMLElement).style.maxWidth = '800px';
        (articleContainer as HTMLElement).style.fontSize = '1.125rem';
        (articleContainer as HTMLElement).style.lineHeight = '1.8';
      }

      // 添加专注模式样式
      const style = document.createElement('style');
      style.id = 'focus-mode-styles';
      style.textContent = `
        .focus-mode {
          background: #fafafa !important;
        }
        .focus-mode.dark {
          background: #0f172a !important;
        }
        .focus-mode article {
          margin: 2rem auto !important;
          padding: 2rem !important;
          background: white !important;
          border-radius: 0.75rem !important;
          box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1) !important;
        }
        .focus-mode.dark article {
          background: #1e293b !important;
          box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.3) !important;
        }
        .focus-mode article p {
          font-size: 1.125rem !important;
          line-height: 1.8 !important;
          margin-bottom: 1.5rem !important;
        }
        .focus-mode article h1,
        .focus-mode article h2,
        .focus-mode article h3 {
          margin-top: 2rem !important;
          margin-bottom: 1rem !important;
        }
      `;
      document.head.appendChild(style);

    } else {
      // 退出专注模式
      document.body.classList.remove('focus-mode');
      
      // 恢复隐藏的元素
      const elementsToShow = [
        'header',
        'nav',
        'aside',
        '.blog-sidebar',
        '.blog-interactions',
        '.related-posts',
        '.blog-comments',
        'footer',
      ];

      elementsToShow.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => {
          (el as HTMLElement).style.display = '';
        });
      });

      // 恢复文章容器样式
      const articleContainer = document.querySelector('article');
      if (articleContainer) {
        (articleContainer as HTMLElement).style.maxWidth = '';
        (articleContainer as HTMLElement).style.fontSize = '';
        (articleContainer as HTMLElement).style.lineHeight = '';
      }

      // 移除专注模式样式
      const style = document.getElementById('focus-mode-styles');
      if (style) {
        style.remove();
      }
    }

    return () => {
      // 清理函数
      if (isFocusMode) {
        document.body.classList.remove('focus-mode');
        const style = document.getElementById('focus-mode-styles');
        if (style) {
          style.remove();
        }
      }
    };
  }, [isFocusMode]);

  const toggleFocusMode = () => {
    setIsFocusMode(!isFocusMode);
  };

  if (!isVisible) return null;

  return (
    <button
      onClick={toggleFocusMode}
      className={cn(
        'fixed top-1/2 right-4 transform -translate-y-1/2 z-50',
        'w-12 h-12 rounded-full transition-all duration-300',
        'bg-white dark:bg-dark-800 border border-mystical-200 dark:border-dark-600',
        'shadow-mystical-lg hover:shadow-mystical-xl',
        'text-mystical-600 dark:text-mystical-400',
        'hover:text-mystical-700 dark:hover:text-mystical-300',
        'hover:-translate-y-1 hover:scale-105',
        'flex items-center justify-center group',
        isFocusMode && 'bg-mystical-500 text-white border-mystical-500',
        className
      )}
      title={isFocusMode ? t('exitFocusMode') : t('enterFocusMode')}
    >
      {isFocusMode ? (
        <Minimize className="w-5 h-5 transition-transform group-hover:scale-110" />
      ) : (
        <Maximize className="w-5 h-5 transition-transform group-hover:scale-110" />
      )}
      
      {/* Tooltip */}
      <div className={cn(
        'absolute right-full mr-3 px-3 py-2 rounded-lg',
        'bg-dark-900 text-white text-sm font-medium',
        'opacity-0 group-hover:opacity-100 transition-opacity duration-200',
        'pointer-events-none whitespace-nowrap',
        'before:content-[""] before:absolute before:left-full before:top-1/2',
        'before:transform before:-translate-y-1/2 before:border-4',
        'before:border-transparent before:border-l-dark-900'
      )}>
        {isFocusMode ? t('exitFocusMode') : t('enterFocusMode')}
      </div>
    </button>
  );
}

// 专注阅读模式的快捷键支持
export function useFocusModeShortcut() {
  const [isFocusMode, setIsFocusMode] = useState(false);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // F11 或 Ctrl/Cmd + Shift + F 切换专注模式
      if (
        event.key === 'F11' ||
        ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'F')
      ) {
        event.preventDefault();
        setIsFocusMode(prev => !prev);
      }
      
      // Escape 退出专注模式
      if (event.key === 'Escape' && isFocusMode) {
        setIsFocusMode(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [isFocusMode]);

  return { isFocusMode, setIsFocusMode };
}

// 阅读进度增强组件
export function EnhancedReadingProgress({ className }: { className?: string }) {
  const [progress, setProgress] = useState(0);
  const [readingTime, setReadingTime] = useState(0);
  const [timeLeft, setTimeLeft] = useState(0);

  useEffect(() => {
    const updateProgress = () => {
      const article = document.querySelector('article');
      if (!article) return;

      const rect = article.getBoundingClientRect();
      const articleTop = rect.top + window.pageYOffset;
      const articleHeight = rect.height;
      const windowHeight = window.innerHeight;
      const scrollTop = window.pageYOffset;

      // 计算阅读进度
      const startProgress = articleTop - windowHeight;
      const endProgress = articleTop + articleHeight;

      if (scrollTop < startProgress) {
        setProgress(0);
      } else if (scrollTop > endProgress) {
        setProgress(100);
      } else {
        const currentProgress = ((scrollTop - startProgress) / (endProgress - startProgress)) * 100;
        setProgress(Math.min(Math.max(currentProgress, 0), 100));
      }

      // 计算预估剩余阅读时间
      const totalWords = article.textContent?.split(/\s+/).length || 0;
      const wordsPerMinute = 200; // 平均阅读速度
      const totalReadingTime = Math.ceil(totalWords / wordsPerMinute);
      const remainingTime = Math.ceil(totalReadingTime * (1 - progress / 100));
      
      setReadingTime(totalReadingTime);
      setTimeLeft(remainingTime);
    };

    const handleScroll = () => {
      requestAnimationFrame(updateProgress);
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    updateProgress(); // 初始计算

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [progress]);

  return (
    <div className={cn(
      'fixed bottom-4 left-4 z-50 bg-white dark:bg-dark-800',
      'border border-mystical-200 dark:border-dark-600 rounded-lg',
      'shadow-mystical-lg p-3 min-w-[200px]',
      'transition-all duration-300',
      className
    )}>
      {/* Progress Bar */}
      <div className="mb-2">
        <div className="flex items-center justify-between text-xs text-mystical-500 dark:text-mystical-400 mb-1">
          <span>{t('readingProgress')}</span>
          <span>{Math.round(progress)}%</span>
        </div>
        <div className="w-full bg-mystical-100 dark:bg-dark-600 rounded-full h-2">
          <div
            className="bg-mystical-500 h-2 rounded-full transition-all duration-300"
            style={{ width: `${progress}%` }}
          />
        </div>
      </div>

      {/* Reading Time Info */}
      <div className="flex items-center justify-between text-xs text-mystical-600 dark:text-mystical-300">
        <span>{t('totalTime')}: {readingTime}min</span>
        <span>{t('timeLeft')}: {timeLeft}min</span>
      </div>
    </div>
  );
}
