'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { useTranslations } from 'next-intl';
import { Calendar, Clock, Eye } from 'lucide-react';
import { BlogPost } from '@/types';
import { cn } from '@/lib/utils';

interface RelatedPostsProps {
  posts: BlogPost[];
  className?: string;
}

export function RelatedPosts({ posts, className }: RelatedPostsProps) {
  const t = useTranslations('blog');

  if (!posts || posts.length === 0) {
    return null;
  }

  const formatDate = (date: Date, locale: string) => {
    return new Intl.DateTimeFormat(locale, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    }).format(date);
  };

  return (
    <section className={cn('bg-mystical-50 dark:bg-dark-800 py-16', className)}>
      <div className="mx-auto max-w-6xl px-4 md:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-2xl md:text-3xl font-bold font-serif text-mystical-900 dark:text-white mb-4">
            {t('relatedPosts')}
          </h2>
          <p className="text-base text-mystical-600 dark:text-mystical-300 max-w-2xl mx-auto">
            {t('relatedPostsDescription')}
          </p>
        </div>

        {/* Posts Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {posts.slice(0, 3).map((post) => (
            <article
              key={post.id}
              className={cn(
                'group bg-white dark:bg-dark-900 rounded-xl overflow-hidden',
                'border border-mystical-200 dark:border-dark-700',
                'hover:shadow-mystical-lg hover:transform hover:-translate-y-2',
                'transition-all duration-300 ease-out'
              )}
            >
              {/* Post Image */}
              {post.coverImage && (
                <div className="relative overflow-hidden">
                  <Link href={`/blog/${post.category.slug}/${post.slug}`}>
                    <Image
                      src={post.coverImage}
                      alt={post.title}
                      width={400}
                      height={225}
                      className="w-full aspect-[16/9] object-cover transition-transform duration-300 group-hover:scale-105"
                      sizes="(max-width: 768px) 100vw, (max-width: 1024px) 50vw, 33vw"
                    />
                  </Link>
                  
                  {/* Category Badge */}
                  <div className="absolute top-4 left-4">
                    <Link
                      href={`/blog/${post.category.slug}`}
                      className={cn(
                        'inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold',
                        'bg-white/90 backdrop-blur-sm text-mystical-700',
                        'hover:bg-white transition-colors',
                        'uppercase tracking-wide'
                      )}
                    >
                      {post.category.name}
                    </Link>
                  </div>
                </div>
              )}

              {/* Post Content */}
              <div className="p-6">
                {/* Title */}
                <h3 className="text-lg font-bold font-serif text-mystical-900 dark:text-white leading-[1.4] mb-3 line-clamp-2">
                  <Link
                    href={`/blog/${post.category.slug}/${post.slug}`}
                    className="hover:text-mystical-700 dark:hover:text-mystical-300 transition-colors"
                  >
                    {post.title}
                  </Link>
                </h3>

                {/* Excerpt */}
                <p className="text-sm text-mystical-600 dark:text-mystical-300 leading-[1.5] mb-4 line-clamp-3">
                  {post.excerpt}
                </p>

                {/* Meta Information */}
                <div className="flex items-center justify-between text-xs text-mystical-500 dark:text-mystical-400">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-1">
                      <Calendar className="w-3 h-3" />
                      <span>{formatDate(post.publishedAt || post.createdAt, post.locale)}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="w-3 h-3" />
                      <span>{post.readingTime} {t('readTime')}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-1">
                    <Eye className="w-3 h-3" />
                    <span>{post.viewCount}</span>
                  </div>
                </div>

                {/* Tags */}
                {post.tags.length > 0 && (
                  <div className="flex flex-wrap gap-1 mt-4">
                    {post.tags.slice(0, 2).map((tag) => (
                      <Link
                        key={tag.id}
                        href={`/blog/tag/${tag.slug}`}
                        className={cn(
                          'inline-flex items-center px-2 py-1 rounded-md text-xs',
                          'bg-mystical-50 dark:bg-dark-700 text-mystical-600 dark:text-mystical-400',
                          'hover:bg-mystical-100 dark:hover:bg-dark-600 transition-colors'
                        )}
                      >
                        #{tag.name}
                      </Link>
                    ))}
                    {post.tags.length > 2 && (
                      <span className="text-xs text-mystical-500 dark:text-mystical-400 px-2 py-1">
                        +{post.tags.length - 2}
                      </span>
                    )}
                  </div>
                )}

                {/* Read More Link */}
                <div className="mt-4 pt-4 border-t border-mystical-100 dark:border-dark-700">
                  <Link
                    href={`/blog/${post.category.slug}/${post.slug}`}
                    className={cn(
                      'inline-flex items-center text-sm font-medium',
                      'text-mystical-600 dark:text-mystical-400',
                      'hover:text-mystical-700 dark:hover:text-mystical-300',
                      'transition-colors group'
                    )}
                  >
                    {t('readMore')}
                    <span className="ml-1 transition-transform group-hover:translate-x-1">
                      →
                    </span>
                  </Link>
                </div>
              </div>
            </article>
          ))}
        </div>

        {/* View All Link */}
        {posts.length > 3 && (
          <div className="text-center mt-12">
            <Link
              href="/blog"
              className={cn(
                'inline-flex items-center px-6 py-3 rounded-lg',
                'bg-mystical-500 hover:bg-mystical-600 text-white',
                'font-medium transition-colors',
                'hover:transform hover:-translate-y-1 hover:shadow-mystical'
              )}
            >
              {t('viewAllPosts')}
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}
