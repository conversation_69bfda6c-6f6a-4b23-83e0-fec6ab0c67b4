'use client';

import React, { useState, useEffect } from 'react';
import { ChevronRight, List, BookOpen } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { BlogPost, TableOfContentsItem } from '@/types';
import { cn } from '@/lib/utils';

interface BlogTableOfContentsProps {
  post: BlogPost;
  className?: string;
  maxLevel?: number;
  showOnDesktop?: boolean;
  showOnMobile?: boolean;
}

export function BlogTableOfContents({
  post,
  className,
  maxLevel = 3,
  showOnDesktop = true,
  showOnMobile = true,
}: BlogTableOfContentsProps) {
  const t = useTranslations('blog');
  const [toc, setToc] = useState<TableOfContentsItem[]>([]);
  const [activeId, setActiveId] = useState<string>('');
  const [isOpen, setIsOpen] = useState(false);

  // 从文章内容生成目录
  useEffect(() => {
    // 如果文章已有目录数据，直接使用
    if (post.tableOfContents && post.tableOfContents.length > 0) {
      setToc(post.tableOfContents);
      return;
    }

    // 否则从DOM中提取标题
    const headings = Array.from(
      document.querySelectorAll('article h1, article h2, article h3, article h4, article h5, article h6')
    ).filter((heading) => {
      const level = parseInt(heading.tagName.charAt(1));
      return level <= maxLevel;
    });

    const tocItems: TableOfContentsItem[] = headings.map((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      let id = heading.id;
      
      // 如果没有id，生成一个
      if (!id) {
        id = `heading-${index}`;
        heading.id = id;
      }

      return {
        id,
        title: heading.textContent || '',
        level,
        anchor: `#${id}`,
      };
    });

    setToc(tocItems);
  }, [post, maxLevel]);

  // 监听滚动，更新活跃的标题
  useEffect(() => {
    if (toc.length === 0) return;

    const observer = new IntersectionObserver(
      (entries) => {
        // 找到当前可见的标题
        const visibleEntries = entries.filter(entry => entry.isIntersecting);
        if (visibleEntries.length > 0) {
          // 选择最靠近顶部的标题
          const topEntry = visibleEntries.reduce((prev, current) => {
            return prev.boundingClientRect.top < current.boundingClientRect.top ? prev : current;
          });
          setActiveId(topEntry.target.id);
        }
      },
      {
        rootMargin: '-10% 0% -80% 0%',
        threshold: [0, 0.25, 0.5, 0.75, 1],
      }
    );

    toc.forEach((item) => {
      const element = document.getElementById(item.id);
      if (element) {
        observer.observe(element);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, [toc]);

  const scrollToHeading = (id: string) => {
    const element = document.getElementById(id);
    if (element) {
      const offsetTop = element.offsetTop - 120; // 考虑固定头部的高度
      window.scrollTo({
        top: offsetTop,
        behavior: 'smooth',
      });
    }
    setIsOpen(false);
  };

  if (toc.length === 0) return null;

  const TocContent = () => (
    <nav className="space-y-1">
      {toc.map((item) => (
        <button
          key={item.id}
          onClick={() => scrollToHeading(item.id)}
          className={cn(
            'block w-full text-left py-2 text-sm transition-all duration-200',
            'border-l-2 border-transparent hover:border-mystical-300',
            activeId === item.id
              ? 'text-mystical-800 dark:text-white border-mystical-500 font-medium bg-mystical-50 dark:bg-dark-700 -ml-4 pl-4'
              : 'text-mystical-600 dark:text-mystical-400 hover:text-mystical-800 dark:hover:text-mystical-200',
            {
              'pl-0': item.level === 1,
              'pl-4': item.level === 2,
              'pl-8': item.level === 3,
              'pl-12': item.level === 4,
              'pl-16': item.level === 5,
              'pl-20': item.level === 6,
            }
          )}
        >
          <span className="line-clamp-2 leading-[1.4]">{item.title}</span>
        </button>
      ))}
    </nav>
  );

  return (
    <>
      {/* Desktop TOC - Medium风格浮动目录 */}
      {showOnDesktop && (
        <div
          className={cn(
            'hidden xl:block fixed left-8 top-1/2 transform -translate-y-1/2',
            'w-64 max-h-[60vh] overflow-y-auto',
            'bg-white dark:bg-dark-800 border border-mystical-200 dark:border-dark-600',
            'rounded-xl shadow-mystical-lg p-6',
            'transition-all duration-300 hover:shadow-mystical-xl',
            className
          )}
        >
          <div className="flex items-center gap-2 mb-4 pb-3 border-b border-mystical-200 dark:border-dark-600">
            <BookOpen className="w-4 h-4 text-mystical-500" />
            <h3 className="text-sm font-semibold text-mystical-900 dark:text-white">
              {t('tableOfContents')}
            </h3>
          </div>
          <TocContent />
        </div>
      )}

      {/* Mobile TOC */}
      {showOnMobile && (
        <div className="xl:hidden">
          {/* Toggle Button */}
          <button
            onClick={() => setIsOpen(!isOpen)}
            className={cn(
              'fixed right-4 top-24 z-50 p-3 rounded-full',
              'bg-white dark:bg-dark-800 border border-mystical-200 dark:border-dark-600',
              'shadow-mystical-lg hover:shadow-mystical-xl',
              'transition-all duration-300 hover:-translate-y-1',
              'text-mystical-600 dark:text-mystical-400'
            )}
            title={t('tableOfContents')}
          >
            <List className="w-5 h-5" />
          </button>

          {/* Mobile TOC Overlay */}
          {isOpen && (
            <div className="fixed inset-0 z-40 xl:hidden">
              <div
                className="absolute inset-0 bg-black/50 backdrop-blur-sm"
                onClick={() => setIsOpen(false)}
              />
              <div className="absolute right-4 top-32 w-80 max-w-[calc(100vw-2rem)] max-h-[60vh] overflow-y-auto bg-white dark:bg-dark-800 border border-mystical-200 dark:border-dark-600 rounded-xl shadow-mystical-xl">
                {/* Header */}
                <div className="sticky top-0 bg-white dark:bg-dark-800 border-b border-mystical-200 dark:border-dark-600 p-4 rounded-t-xl">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <BookOpen className="w-4 h-4 text-mystical-500" />
                      <h3 className="text-sm font-semibold text-mystical-900 dark:text-white">
                        {t('tableOfContents')}
                      </h3>
                    </div>
                    <button
                      onClick={() => setIsOpen(false)}
                      className="text-mystical-400 hover:text-mystical-600 dark:hover:text-mystical-300 p-1 rounded-md hover:bg-mystical-50 dark:hover:bg-dark-700 transition-colors"
                    >
                      <ChevronRight className="w-4 h-4 rotate-45" />
                    </button>
                  </div>
                </div>
                
                {/* Content */}
                <div className="p-4">
                  <TocContent />
                </div>
              </div>
            </div>
          )}
        </div>
      )}
    </>
  );
}
